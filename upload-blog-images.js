#!/usr/bin/env node

/**
 * Upload Blog Images to Cloudflare R2 and Update Database
 * 
 * This script uploads the generated blog images to Cloudflare R2 and updates
 * the database with the custom domain URLs.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');

// Configuration
const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

const CLOUDFLARE_R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucketName: 'directory-findcaraccidentlawyers-live-040925',
  region: 'auto',
  accessKeyId: 'c789b94077ced966f3c5491e846fefa3',
  secretAccessKey: '****************************************************************',
  publicUrlBase: 'https://images.findcaraccidentlawyers.org'
};

// Blog posts with their generated image URLs
const BLOG_IMAGES = [
  {
    id: '7f008e52-1c30-4a0f-8b42-5facef74eb8a',
    slug: 'what-to-do-after-car-accident-essential-steps',
    imageUrl: 'https://replicate.delivery/xezq/7eGhWK9AeOgSrUrPyoKOkhoBSDQaRsxvmGAfH6fsZdpzYYHVB/out-0.webp'
  },
  {
    id: 'f4f11379-077c-4699-91f1-2057d5ef6fe5',
    slug: 'is-it-worth-getting-attorney-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/lORFewuslh1CdqmZBPOuluFRTeKLfIQOjQm3Jl31aEFqMsjqA/out-0.webp'
  },
  {
    id: '2ac0778c-9a67-4b4f-8e83-767ea17b2561',
    slug: 'what-to-do-after-car-accident-complete-guide',
    imageUrl: 'https://replicate.delivery/xezq/8pc9tIx0jy6fAScockf05iQQBHlNb45LRqcsDeNuzJSBNsjqA/out-0.webp'
  },
  {
    id: 'aa259e58-c0f3-42f8-a79f-b29bb57c6e08',
    slug: 'how-many-car-accidents-per-day-usa-statistics',
    imageUrl: 'https://replicate.delivery/xezq/OJKteUz69mWbaim7AeTUktXiYhieutoO6usWdNYbOdWQNsjqA/out-0.webp'
  },
  {
    id: 'dab130e4-e742-4e77-b6c1-a16846c1f2ef',
    slug: 'how-much-car-insurance-go-up-after-accident',
    imageUrl: 'https://replicate.delivery/xezq/ucemkzu2z41LbKDvSav5p4DOUpQR5o4k6MPV5NCoLCxYD7oKA/out-0.webp'
  },
  {
    id: '17f20f9d-3ffe-4064-8735-63f65a16bebe',
    slug: 'should-i-get-lawyer-car-accident-decision-guide',
    imageUrl: 'https://replicate.delivery/xezq/py8U6Xo2GGZXM5WKIxH6vSPo1u30t4YeTiHHJLmDQlKcD7oKA/out-0.webp'
  },
  {
    id: 'c24461a1-0ec9-4a30-831b-f628275a46ad',
    slug: 'how-much-car-accident-lawyer-cost-fee-guide',
    imageUrl: 'https://replicate.delivery/xezq/Jmap9H1jv5pIFVNW8i1ya4aamGORSS2ZewCTrQ6U7JCgD7oKA/out-0.webp'
  },
  {
    id: 'fabf06ca-f12f-487d-b76c-a358a153f727',
    slug: 'should-i-get-lawyer-minor-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/9IaEKHHvDkICNZSjrCRmq3KRWuQKFWGQu5T3BfwYXUXsD7oKA/out-0.webp'
  },
  {
    id: 'c43d94ce-ce79-41b9-8240-01fe7032f821',
    slug: 'how-to-settle-car-accident-claim-without-lawyer',
    imageUrl: 'https://replicate.delivery/xezq/j5MJQGfBedlvakxRdlt1vFVhcPpoec5c5ILVonPunaxBPsjqA/out-0.webp'
  },
  {
    id: '25c9971f-885b-49fe-bc2b-996f0fe3b6d0',
    slug: 'what-does-car-accident-lawyer-do-legal-services',
    imageUrl: 'https://replicate.delivery/xezq/9gtmoCSHtRIvAhMXxkhcbrv6QhMXwIHqfJnDLOoukvz2D7oKA/out-0.webp'
  }
];

// Initialize clients
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

function initializeR2Client() {
  return new AWS.S3({
    endpoint: CLOUDFLARE_R2_CONFIG.endpoint,
    accessKeyId: CLOUDFLARE_R2_CONFIG.accessKeyId,
    secretAccessKey: CLOUDFLARE_R2_CONFIG.secretAccessKey,
    region: CLOUDFLARE_R2_CONFIG.region,
    signatureVersion: 'v4',
    s3ForcePathStyle: true
  });
}

// Upload image to R2 and return custom domain URL
async function uploadImageToR2(imageUrl, filename) {
  console.log(`☁️ Uploading ${filename} to R2...`);
  
  try {
    // Download image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status}`);
    }
    
    const imageBuffer = Buffer.from(await response.arrayBuffer());
    const r2Client = initializeR2Client();
    
    // Upload to R2
    const uploadParams = {
      Bucket: CLOUDFLARE_R2_CONFIG.bucketName,
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/webp',
      ACL: 'public-read'
    };
    
    await r2Client.upload(uploadParams).promise();
    
    // Return custom domain URL
    const customUrl = `${CLOUDFLARE_R2_CONFIG.publicUrlBase}/${filename}`;
    console.log(`✅ Uploaded successfully: ${customUrl}`);
    
    return customUrl;
    
  } catch (error) {
    console.error(`❌ Upload failed: ${error.message}`);
    return null;
  }
}

// Update blog post with image URL
async function updateBlogPost(blogId, imageUrl) {
  console.log(`💾 Updating blog post ${blogId}...`);
  
  try {
    const { error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .update({ 
        featured_image: imageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', blogId);
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Blog post updated successfully');
    return true;
    
  } catch (error) {
    console.error(`❌ Update failed: ${error.message}`);
    return false;
  }
}

// Process all blog posts
async function processAllBlogPosts() {
  console.log('🚀 Starting blog image upload process...\n');
  
  let successCount = 0;
  let failureCount = 0;
  
  for (const blogPost of BLOG_IMAGES) {
    console.log(`\n🔄 Processing: "${blogPost.slug}"`);
    
    // Generate filename
    const timestamp = Date.now();
    const filename = `blog-images/${blogPost.slug}.webp`;
    
    // Upload to R2
    const customUrl = await uploadImageToR2(blogPost.imageUrl, filename);
    if (!customUrl) {
      failureCount++;
      continue;
    }
    
    // Update database
    const success = await updateBlogPost(blogPost.id, customUrl);
    
    if (success) {
      successCount++;
      console.log(`✅ Successfully processed: "${blogPost.slug}"`);
      console.log(`🔗 Image URL: ${customUrl}`);
    } else {
      failureCount++;
    }
    
    // Small delay to avoid overwhelming the services
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 Process completed:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${failureCount}`);
  console.log(`   📝 Total: ${BLOG_IMAGES.length}`);
}

// Main execution
if (require.main === module) {
  processAllBlogPosts().catch(console.error);
}

module.exports = { 
  uploadImageToR2, 
  updateBlogPost, 
  processAllBlogPosts 
};
