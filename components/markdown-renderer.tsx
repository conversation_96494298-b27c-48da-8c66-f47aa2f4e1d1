import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { processMarkdownWithLinks } from '@/lib/utils/internal-links'
import { useEffect, useState } from 'react'

interface MarkdownRendererProps {
  content: string
  currentSlug?: string
  className?: string
}

export function MarkdownRenderer({ content, currentSlug, className = '' }: MarkdownRendererProps) {
  const [processedContent, setProcessedContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const processContent = async () => {
      try {
        const processed = await processMarkdownWithLinks(content, currentSlug)
        setProcessedContent(processed)
      } catch (error) {
        console.error('Error processing markdown content:', error)
        setProcessedContent(content) // Fallback to original content
      } finally {
        setIsLoading(false)
      }
    }

    processContent()
  }, [content, currentSlug])

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6 mb-4"></div>
      </div>
    )
  }

  return (
    <ReactMarkdown
      className={`prose prose-xl max-w-none text-lg leading-relaxed ${className}`}
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeRaw]}
      components={{
        // Custom styling for different elements
        h1: ({ children }) => (
          <h1 className="text-4xl font-bold mt-8 mb-6 text-foreground">{children}</h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-3xl font-semibold mt-8 mb-4 text-foreground">{children}</h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-2xl font-semibold mt-6 mb-3 text-foreground">{children}</h3>
        ),
        h4: ({ children }) => (
          <h4 className="text-xl font-semibold mt-4 mb-2 text-foreground">{children}</h4>
        ),
        p: ({ children }) => (
          <p className="mb-4 leading-relaxed text-lg text-foreground">{children}</p>
        ),
        strong: ({ children }) => (
          <strong className="font-bold text-foreground">{children}</strong>
        ),
        em: ({ children }) => (
          <em className="italic text-foreground">{children}</em>
        ),
        ul: ({ children }) => (
          <ul className="list-disc list-inside mb-4 space-y-2 text-foreground">{children}</ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal list-inside mb-4 space-y-2 text-foreground">{children}</ol>
        ),
        li: ({ children }) => (
          <li className="text-lg leading-relaxed text-foreground">{children}</li>
        ),
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-primary pl-4 italic my-4 text-muted-foreground">
            {children}
          </blockquote>
        ),
        code: ({ children, className }) => {
          const isInline = !className
          if (isInline) {
            return (
              <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono text-foreground">
                {children}
              </code>
            )
          }
          return (
            <code className="block bg-muted p-4 rounded-lg text-sm font-mono overflow-x-auto text-foreground">
              {children}
            </code>
          )
        },
        pre: ({ children }) => (
          <pre className="bg-muted p-4 rounded-lg overflow-x-auto mb-4">
            {children}
          </pre>
        ),
        a: ({ href, children }) => (
          <a
            href={href}
            className="text-primary hover:underline font-medium"
            target={href?.startsWith('http') ? '_blank' : undefined}
            rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
          >
            {children}
          </a>
        ),
        table: ({ children }) => (
          <div className="overflow-x-auto mb-4">
            <table className="min-w-full border-collapse border border-border">
              {children}
            </table>
          </div>
        ),
        th: ({ children }) => (
          <th className="border border-border px-4 py-2 bg-muted font-semibold text-left">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="border border-border px-4 py-2">
            {children}
          </td>
        ),
      }}
    >
      {processedContent}
    </ReactMarkdown>
  )
}
