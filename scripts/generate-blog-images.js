#!/usr/bin/env node

/**
 * Generate Featured Images for Blog Posts
 * 
 * This script reads blog posts from Supabase, generates featured images using
 * Replicate Flux MCP, uploads them to Cloudflare R2, and updates the database.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  tableName: 'findcaraccidentattorneys-blog'
};

const R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucket: 'directory-findcaraccidentlawyers-live-040925',
  customDomain: 'https://images.findcaraccidentlawyers.org',
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  region: 'auto'
};

const REPLICATE_CONFIG = {
  token: process.env.REPLICATE_API_TOKEN
};

// Initialize clients
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

const s3 = new AWS.S3({
  endpoint: R2_CONFIG.endpoint,
  accessKeyId: R2_CONFIG.accessKeyId,
  secretAccessKey: R2_CONFIG.secretAccessKey,
  region: R2_CONFIG.region,
  signatureVersion: 'v4',
});

/**
 * Generate image prompt from blog title and content
 */
function generateImagePrompt(title, content) {
  // Extract first few sentences for context
  const contentSummary = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .split('.').slice(0, 2).join('.') // First 2 sentences
    .substring(0, 200); // Limit length

  const prompt = `${title} - ${contentSummary} - IMPORTANT: Generate a candid, raw mobile phone style photograph without any text, words, or written content visible in the image. Real-world car accident scene or legal consultation setting. Professional but authentic documentary style photography.`;
  
  return prompt;
}

/**
 * Generate image using Replicate Flux MCP
 * Note: This function simulates MCP usage. In actual implementation,
 * you would use the MCP tool directly from the AI assistant.
 */
async function generateImage(prompt) {
  try {
    console.log('🎨 Using Replicate Flux MCP to generate image...');
    console.log('📝 Prompt:', prompt);

    // This is a placeholder for MCP tool usage
    // In the actual implementation, you would call the MCP tool like this:
    /*
    const result = await use_mcp_tool('replicate-flux-mcp', 'generate_image', {
      prompt: prompt,
      aspect_ratio: '1:1',
      output_format: 'webp',
      output_quality: 80,
      num_inference_steps: 4,
      disable_safety_checker: false
    });
    return result.imageUrl;
    */

    // For now, we'll use the direct Replicate API as fallback
    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${REPLICATE_CONFIG.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: "black-forest-labs/flux-dev",
        input: {
          prompt: prompt,
          aspect_ratio: "1:1",
          output_format: "webp",
          output_quality: 80,
          num_inference_steps: 4,
          disable_safety_checker: false
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Replicate API error: ${response.status} ${response.statusText}`);
    }

    const prediction = await response.json();

    // Wait for completion
    let result = prediction;
    while (result.status === 'starting' || result.status === 'processing') {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: {
          'Authorization': `Token ${REPLICATE_CONFIG.token}`,
        }
      });

      result = await statusResponse.json();
    }

    if (result.status === 'succeeded' && result.output && result.output.length > 0) {
      return result.output[0]; // Return the first generated image URL
    } else {
      throw new Error(`Image generation failed: ${result.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error generating image:', error);
    throw error;
  }
}

/**
 * Download image from URL
 */
async function downloadImage(imageUrl) {
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }
  return response.buffer();
}

/**
 * Upload image to Cloudflare R2
 */
async function uploadToR2(imageBuffer, slug) {
  const key = `blog-images/${slug}.webp`;
  
  const uploadParams = {
    Bucket: R2_CONFIG.bucket,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/webp',
    CacheControl: 'public, max-age=31536000', // 1 year cache
  };

  try {
    await s3.upload(uploadParams).promise();
    return `${R2_CONFIG.customDomain}/${key}`;
  } catch (error) {
    console.error('Error uploading to R2:', error);
    throw error;
  }
}

/**
 * Update blog post with featured image URL
 */
async function updateBlogPost(blogId, imageUrl) {
  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ featured_image: imageUrl })
    .eq('id', blogId);

  if (error) {
    throw new Error(`Failed to update blog post: ${error.message}`);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Starting blog image generation...');

    // Check required environment variables
    if (!REPLICATE_CONFIG.token) {
      throw new Error('REPLICATE_API_TOKEN environment variable is required');
    }
    if (!R2_CONFIG.accessKeyId || !R2_CONFIG.secretAccessKey) {
      throw new Error('R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY environment variables are required');
    }

    // Fetch blog posts without featured images
    const { data: blogs, error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .select('id, title, slug, content, excerpt')
      .is('featured_image', null)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch blog posts: ${error.message}`);
    }

    if (!blogs || blogs.length === 0) {
      console.log('✅ No blog posts found without featured images.');
      return;
    }

    console.log(`📝 Found ${blogs.length} blog posts without featured images.`);

    // Process each blog post
    for (let i = 0; i < blogs.length; i++) {
      const blog = blogs[i];
      console.log(`\n🔄 Processing ${i + 1}/${blogs.length}: "${blog.title}"`);

      try {
        // Generate image prompt
        const prompt = generateImagePrompt(blog.title, blog.content || blog.excerpt || '');
        console.log(`📝 Generated prompt: ${prompt.substring(0, 100)}...`);

        // Generate image
        console.log('🎨 Generating image...');
        const imageUrl = await generateImage(prompt);
        console.log(`✅ Image generated: ${imageUrl}`);

        // Download image
        console.log('⬇️ Downloading image...');
        const imageBuffer = await downloadImage(imageUrl);

        // Upload to R2
        console.log('☁️ Uploading to R2...');
        const r2Url = await uploadToR2(imageBuffer, blog.slug);
        console.log(`✅ Uploaded to R2: ${r2Url}`);

        // Update database
        console.log('💾 Updating database...');
        await updateBlogPost(blog.id, r2Url);
        console.log(`✅ Updated blog post: ${blog.title}`);

        // Add delay to avoid rate limiting
        if (i < blogs.length - 1) {
          console.log('⏳ Waiting 5 seconds before next image...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }

      } catch (error) {
        console.error(`❌ Error processing "${blog.title}":`, error.message);
        // Continue with next blog post
      }
    }

    console.log('\n🎉 Blog image generation completed!');

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
